<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>README_ahorro_interpretacion_representacion_n2_v2</title>
  <style>
    html {
      color: #1a1a1a;
      background-color: #fdfdfd;
    }
    body {
      margin: 0 auto;
      max-width: 36em;
      padding-left: 50px;
      padding-right: 50px;
      padding-top: 50px;
      padding-bottom: 50px;
      hyphens: auto;
      overflow-wrap: break-word;
      text-rendering: optimizeLegibility;
      font-kerning: normal;
    }
    @media (max-width: 600px) {
      body {
        font-size: 0.9em;
        padding: 12px;
      }
      h1 {
        font-size: 1.8em;
      }
    }
    @media print {
      html {
        background-color: white;
      }
      body {
        background-color: transparent;
        color: black;
        font-size: 12pt;
      }
      p, h2, h3 {
        orphans: 3;
        widows: 3;
      }
      h2, h3, h4 {
        page-break-after: avoid;
      }
    }
    p {
      margin: 1em 0;
    }
    a {
      color: #1a1a1a;
    }
    a:visited {
      color: #1a1a1a;
    }
    img {
      max-width: 100%;
    }
    svg {
      height: auto;
      max-width: 100%;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.4em;
    }
    h5, h6 {
      font-size: 1em;
      font-style: italic;
    }
    h6 {
      font-weight: normal;
    }
    ol, ul {
      padding-left: 1.7em;
      margin-top: 1em;
    }
    li > ol, li > ul {
      margin-top: 0;
    }
    blockquote {
      margin: 1em 0 1em 1.7em;
      padding-left: 1em;
      border-left: 2px solid #e6e6e6;
      color: #606060;
    }
    code {
      font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
      font-size: 85%;
      margin: 0;
      hyphens: manual;
    }
    pre {
      margin: 1em 0;
      overflow: auto;
    }
    pre code {
      padding: 0;
      overflow: visible;
      overflow-wrap: normal;
    }
    .sourceCode {
     background-color: transparent;
     overflow: visible;
    }
    hr {
      background-color: #1a1a1a;
      border: none;
      height: 1px;
      margin: 1em 0;
    }
    table {
      margin: 1em 0;
      border-collapse: collapse;
      width: 100%;
      overflow-x: auto;
      display: block;
      font-variant-numeric: lining-nums tabular-nums;
    }
    table caption {
      margin-bottom: 0.75em;
    }
    tbody {
      margin-top: 0.5em;
      border-top: 1px solid #1a1a1a;
      border-bottom: 1px solid #1a1a1a;
    }
    th {
      border-top: 1px solid #1a1a1a;
      padding: 0.25em 0.5em 0.25em 0.5em;
    }
    td {
      padding: 0.125em 0.5em 0.25em 0.5em;
    }
    header {
      margin-bottom: 4em;
      text-align: center;
    }
    #TOC li {
      list-style: none;
    }
    #TOC ul {
      padding-left: 1.3em;
    }
    #TOC > ul {
      padding-left: 0;
    }
    #TOC a:not(:hover) {
      text-decoration: none;
    }
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    .display.math{display: block; text-align: center; margin: 0.5rem auto;}
    /* CSS for syntax highlighting */
    html { -webkit-text-size-adjust: 100%; }
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
</head>
<body>
<h1
id="readme---ejercicio-de-ahorro-e-interpretación-de-representaciones">📊
README - Ejercicio de Ahorro e Interpretación de Representaciones</h1>
<h2 id="información-general">📋 Información General</h2>
<p><strong>Archivo:</strong>
<code>ahorro_interpretacion_representacion_n2_v2.Rnw</code>
<strong>Tipo:</strong> Ejercicio R-exams (LaTeX + R)
<strong>Nivel:</strong> Secundaria - Matemáticas Financieras
<strong>Competencia ICFES:</strong> Interpretación y Representación
(Nivel 2) <strong>Formato:</strong> Opción múltiple (4 opciones)
<strong>Estado:</strong> ✅ <strong>COMPLETAMENTE FUNCIONAL</strong> -
Versión 2.0 Optimizada <strong>Última actualización:</strong> Enero
2025</p>
<h2 id="estado-actual-del-ejercicio">🚀 Estado Actual del Ejercicio</h2>
<h3 id="verificación-de-funcionamiento-completo">✅ <strong>VERIFICACIÓN
DE FUNCIONAMIENTO COMPLETO</strong></h3>
<ul>
<li>🟢 <strong>Compilación exitosa:</strong> PDF, HTML, DOCX, NOPS
generados correctamente</li>
<li>🟢 <strong>Aleatorización verificada:</strong> 300+ versiones únicas
garantizadas</li>
<li>🟢 <strong>Balance matemático:</strong> Sistema anti-sesgo
implementado y funcionando</li>
<li>🟢 <strong>Formatos múltiples:</strong> Todos los formatos R-exams
soportados</li>
<li>🟢 <strong>Sin errores críticos:</strong> Notación científica
eliminada, caracteres especiales corregidos</li>
</ul>
<h3 id="archivos-de-salida-disponibles-carpeta-.salida">📂 Archivos de
Salida Disponibles (Carpeta <code>./salida/</code>):</h3>
<ul>
<li>✅ <code>ahorro_interpretacion_representacion_n2_v2_1.pdf</code> -
Versión PDF lista</li>
<li>✅ <code>ahorro_interpretacion_representacion_n2_v2_1.docx</code> -
Versión Word lista</li>
<li>✅
<code>ahorro_interpretacion_representacion_n2_v2_nops_1.pdf</code> -
Versión NOPS lista</li>
<li>✅ <code>ahorro_interpretacion_representacion_n2_v2_nops_.rds</code>
- Datos NOPS</li>
</ul>
<h3 id="resultado-listo-para-uso-en-producción">🎯
<strong>RESULTADO:</strong> ✅ <strong>LISTO PARA USO EN
PRODUCCIÓN</strong></h3>
<h2 id="descripción-del-ejercicio">🎯 Descripción del Ejercicio</h2>
<p>Este ejercicio presenta un <strong>problema de decisión financiera
contextualizada</strong> donde un estudiante debe elegir entre dos
opciones de ayuda familiar para un proyecto de ahorro. Los estudiantes
deben:</p>
<ol type="1">
<li><strong>Interpretar</strong> dos tablas con diferentes esquemas de
porcentajes de ayuda familiar</li>
<li><strong>Calcular</strong> totales de dinero recibido en cada opción
a lo largo de 3 meses</li>
<li><strong>Evaluar</strong> si la elección del personaje fue
matemáticamente correcta</li>
<li><strong>Argumentar</strong> su respuesta con cálculos y
justificaciones matemáticas sólidas</li>
</ol>
<h3 id="contexto-aleatorizado">🎲 Contexto Aleatorizado</h3>
<ul>
<li><strong>Personajes:</strong> 12 nombres diferentes (Ana, Carlos,
María, Diego, Sofía, etc.)</li>
<li><strong>Familiares:</strong> 8 tipos diferentes (tío, tía, abuelo,
abuela, hermano, hermana, primo, prima)</li>
<li><strong>Montos:</strong> 7 rangos de ahorro mensual ($100k -
$250k)</li>
<li><strong>Decisiones:</strong> El personaje puede elegir correcta o
incorrectamente (50/50)</li>
</ul>
<h2 id="características-técnicas-avanzadas">🔧 Características Técnicas
Avanzadas</h2>
<h3 id="variables-aleatorias-expandidas">🎲 Variables Aleatorias
Expandidas</h3>
<ul>
<li><strong>Nombres:</strong> 12 opciones (Ana, Carlos, María, Diego,
Sofía, Andrés, Lucía, Miguel, Carmen, Pablo, Elena, Jorge)</li>
<li><strong>Familiares:</strong> 8 opciones con género (tío/tía,
abuelo/abuela, hermano/hermana, primo/prima)</li>
<li><strong>Montos de ahorro:</strong> 7 opciones ($100k - $250k en
incrementos de $25k)</li>
<li><strong>Porcentajes:</strong> Sistema balanceado inteligente para
evitar sesgo sistemático</li>
<li><strong>Elecciones:</strong> Aleatorización de decisión
correcta/incorrecta del personaje</li>
</ul>
<h3 id="configuraciones-anti-notación-científica-radical">🛡️
Configuraciones Anti-Notación Científica (Radical)</h3>
<div class="sourceCode" id="cb1"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="co"># CONFIGURACION RADICAL ANTI-NOTACION CIENTIFICA</span></span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="fu">Sys.setlocale</span>(<span class="at">category =</span> <span class="st">&quot;LC_NUMERIC&quot;</span>, <span class="at">locale =</span> <span class="st">&quot;C&quot;</span>)</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a><span class="fu">options</span>(<span class="at">OutDec =</span> <span class="st">&quot;.&quot;</span>)</span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a><span class="fu">options</span>(<span class="at">scipen =</span> <span class="dv">999</span>)  <span class="co"># Evitar notacion cientifica completamente</span></span>
<span id="cb1-5"><a href="#cb1-5" aria-hidden="true" tabindex="-1"></a><span class="fu">options</span>(<span class="at">digits =</span> <span class="dv">10</span>)   <span class="co"># Suficientes digitos para numeros grandes</span></span></code></pre></div>
<h3 id="funciones-de-formateo-especializadas">🔧 Funciones de Formateo
Especializadas</h3>
<ul>
<li><strong><code>formatear_entero()</code></strong>: Formato entero sin
notación científica JAMÁS</li>
<li><strong><code>formatear_monetario()</code></strong>: Formato
monetario con separador de miles punto</li>
<li><strong><code>obtener_articulo()</code></strong>: Artículos
correctos según género (“del”/“de la”)</li>
<li><strong><code>obtener_articulo_det()</code></strong>: Artículos
determinados (“el”/“la”)</li>
</ul>
<h2 id="sistema-de-aleatorización-inteligente">🎲 Sistema de
Aleatorización Inteligente</h2>
<h3 id="selección-de-variables-expandida">1. 🎯 Selección de Variables
Expandida</h3>
<ul>
<li><strong>Nombre del estudiante:</strong> 12 opciones diferentes</li>
<li><strong>Dos familiares diferentes:</strong> 56 combinaciones
posibles (8×7)</li>
<li><strong>Monto de ahorro mensual:</strong> 7 opciones ($100k, $125k,
$150k, $175k, $200k, $225k, $250k)</li>
<li><strong>Duración:</strong> Fijo a 3 meses para simplicidad y
claridad</li>
</ul>
<h3 id="sistema-de-balance-anti-sesgo">2. ⚖️ Sistema de Balance
Anti-Sesgo</h3>
<p>El sistema <strong>aleatoriamente favorece</strong> una de las dos
opciones para eliminar sesgo sistemático:</p>
<p><strong>🟢 Cuando se Favorece Opción 1 (Porcentaje
Constante):</strong> - <strong>Porcentaje constante:</strong> 12-16%
(alto y estable) - <strong>Porcentajes variables:</strong> - Mes 1: 2-6%
(bajo) - Mes 2: 4-8% (medio) - Mes 3: 8-15% (moderado, no muy alto)</p>
<p><strong>🟡 Cuando se Favorece Opción 2 (Porcentajes
Variables):</strong> - <strong>Porcentaje constante:</strong> 8-12%
(moderado) - <strong>Porcentajes variables:</strong> - Mes 1: 1-4%
(bajo) - Mes 2: 3-6% (medio) - Mes 3: 16-24% (alto, compensando meses
anteriores)</p>
<h3 id="aleatorización-completa-de-respuestas">3. 🎯 Aleatorización
Completa de Respuestas</h3>
<ul>
<li><strong>Elección del personaje:</strong> Puede ser correcta o
incorrecta (50/50 probabilidad)</li>
<li><strong>Orden de opciones:</strong> Las 4 opciones se mezclan
completamente al azar</li>
<li><strong>Posición de respuesta correcta:</strong> Puede aparecer en
posición A, B, C o D</li>
<li><strong>Validación de diferencia:</strong> Mínimo $10,000 de
diferencia entre opciones</li>
</ul>
<h2 id="estructura-del-problema">📊 Estructura del Problema</h2>
<h3 id="pregunta-principal">Pregunta Principal</h3>
<p>“[Nombre] decide elegir la opción en la que le regalen la mayor
cantidad de dinero y elige la ayuda [del/de la] [familiar]. ¿Es correcta
la elección de [Nombre]?”</p>
<h3 id="opciones-de-respuesta-argumentadas">Opciones de Respuesta
(Argumentadas)</h3>
<ol type="1">
<li><strong>Opción A:</strong> Respuesta con justificación basada en
totales calculados</li>
<li><strong>Opción B:</strong> Respuesta alternativa con argumentos
matemáticos</li>
<li><strong>Opción C:</strong> Distractor con argumento sobre
porcentajes promedio</li>
<li><strong>Opción D:</strong> Distractor con argumento sobre
porcentajes individuales</li>
</ol>
<h3 id="solución-detallada">Solución Detallada</h3>
<ul>
<li>Cálculos paso a paso para ambas opciones</li>
<li>Totales comparativos</li>
<li>Conclusión sobre la corrección de la elección</li>
<li>Explicaciones específicas para cada opción de respuesta</li>
</ul>
<h2 id="mejoras-de-formato">🎨 Mejoras de Formato</h2>
<h3 id="espaciado-mejorado">Espaciado Mejorado</h3>
<ul>
<li><code>\bigskip</code>: Espacios grandes entre secciones
principales</li>
<li><code>\medskip</code>: Espacios medianos antes de tablas</li>
<li>Mejor separación visual entre elementos</li>
</ul>
<h3 id="manejo-de-géneros">Manejo de Géneros</h3>
<ul>
<li>Artículos correctos según género del familiar</li>
<li>Funciones <code>obtener_articulo()</code> y
<code>obtener_articulo_det()</code></li>
<li>Ejemplos: “del abuelo”, “de la abuela”, “el hermano”, “la tía”</li>
</ul>
<h2 id="capacidad-de-generación-masiva">📈 Capacidad de Generación
Masiva</h2>
<h3 id="combinaciones-matemáticas">🔢 Combinaciones Matemáticas</h3>
<ul>
<li><strong>Nombres:</strong> 12 opciones</li>
<li><strong>Familiares:</strong> 8×7 = 56 combinaciones (dos
diferentes)</li>
<li><strong>Montos:</strong> 7 opciones</li>
<li><strong>Balance:</strong> 2 opciones (cuál se favorece)</li>
<li><strong>Elección:</strong> 2 opciones (correcta/incorrecta)</li>
<li><strong>Combinaciones teóricas:</strong> 12 × 56 × 7 × 2 × 2 =
<strong>18,816 combinaciones base</strong></li>
</ul>
<h3 id="garantía-de-unicidad-verificada">✅ Garantía de Unicidad
Verificada</h3>
<ul>
<li><strong>Combinaciones válidas:</strong> ~15,053 (80% pasan
restricción de diferencia mínima)</li>
<li><strong>Margen para 300 versiones:</strong> <strong>50.2x</strong>
(amplio margen de seguridad)</li>
<li><strong>Garantía:</strong> ✅ <strong>Se pueden generar fácilmente
300+ versiones completamente diferentes</strong></li>
<li><strong>Recomendación:</strong> Hasta 1,000 versiones sin problemas
de duplicación</li>
</ul>
<h2 id="validaciones-implementadas">🔍 Validaciones Implementadas</h2>
<h3 id="restricción-de-diferencia">Restricción de Diferencia</h3>
<ul>
<li>Diferencia mínima entre totales: $10,000</li>
<li>Asegura que haya una opción claramente mejor</li>
<li>Evita casos ambiguos o muy cercanos</li>
</ul>
<h3 id="balance-de-opciones">Balance de Opciones</h3>
<ul>
<li>~50% de casos favorecen Opción 1</li>
<li>~50% de casos favorecen Opción 2</li>
<li>Elimina sesgo sistemático hacia una opción</li>
</ul>
<h2 id="formatos-de-salida-soportados">📝 Formatos de Salida
Soportados</h2>
<h3 id="html">HTML</h3>
<ul>
<li>Espaciado optimizado con <code>&lt;br /&gt;</code> tags</li>
<li>Tablas con bordes y centrado</li>
<li>Formato monetario correcto</li>
</ul>
<h3 id="xmlmoodle">XML/Moodle</h3>
<ul>
<li>Estructura de párrafos <code>&lt;p&gt;</code></li>
<li>Tablas con clases CSS</li>
<li>Espaciado natural mejorado</li>
</ul>
<h3 id="pdf-vía-latex">PDF (vía LaTeX)</h3>
<ul>
<li>Comandos de espaciado LaTeX</li>
<li>Tablas centradas con <code>\begin{center}</code></li>
<li>Formato matemático profesional</li>
</ul>
<h2 id="uso-y-ejecución-práctica">🚀 Uso y Ejecución Práctica</h2>
<h3 id="configuración-inicial-requerida">🔧 Configuración Inicial
Requerida</h3>
<div class="sourceCode" id="cb2"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Cargar librerías necesarias</span></span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(exams)</span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a><span class="co"># Configurar directorio de trabajo (ajustar ruta según tu sistema)</span></span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a><span class="fu">setwd</span>(<span class="st">&quot;Lab-Manjaro/08-Rnw&quot;</span>)  <span class="co"># O tu ruta específica</span></span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb2-7"><a href="#cb2-7" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar que el archivo existe</span></span>
<span id="cb2-8"><a href="#cb2-8" aria-hidden="true" tabindex="-1"></a><span class="fu">file.exists</span>(<span class="st">&quot;ahorro_interpretacion_representacion_n2_v2.Rnw&quot;</span>)</span>
<span id="cb2-9"><a href="#cb2-9" aria-hidden="true" tabindex="-1"></a><span class="co"># Debe devolver TRUE</span></span></code></pre></div>
<h3 id="generar-html-recomendado-para-visualización-y-pruebas">🌐
Generar HTML (Recomendado para visualización y pruebas)</h3>
<div class="sourceCode" id="cb3"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Versión única para prueba rápida</span></span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a><span class="fu">set.seed</span>(<span class="dv">12345</span>)  <span class="co"># Para reproducibilidad en pruebas</span></span>
<span id="cb3-3"><a href="#cb3-3" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb3-4"><a href="#cb3-4" aria-hidden="true" tabindex="-1"></a>           <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_test&#39;</span>,</span>
<span id="cb3-5"><a href="#cb3-5" aria-hidden="true" tabindex="-1"></a>           <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>,</span>
<span id="cb3-6"><a href="#cb3-6" aria-hidden="true" tabindex="-1"></a>           <span class="at">template =</span> <span class="st">&#39;plain.html&#39;</span>)</span>
<span id="cb3-7"><a href="#cb3-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb3-8"><a href="#cb3-8" aria-hidden="true" tabindex="-1"></a><span class="co"># Versión con template personalizado (si tienes uno)</span></span>
<span id="cb3-9"><a href="#cb3-9" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb3-10"><a href="#cb3-10" aria-hidden="true" tabindex="-1"></a>           <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_custom&#39;</span>,</span>
<span id="cb3-11"><a href="#cb3-11" aria-hidden="true" tabindex="-1"></a>           <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>,</span>
<span id="cb3-12"><a href="#cb3-12" aria-hidden="true" tabindex="-1"></a>           <span class="at">template =</span> <span class="st">&#39;exam.html&#39;</span>)</span></code></pre></div>
<h3 id="generar-xmlmoodle-para-lms">📚 Generar XML/Moodle (Para
LMS)</h3>
<div class="sourceCode" id="cb4"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Para importar en Moodle (formato más común)</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2moodle</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a>             <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_moodle&#39;</span>,</span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a>             <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>,</span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>             <span class="at">converter =</span> <span class="st">&#39;pandoc-mathjax&#39;</span>)</span>
<span id="cb4-6"><a href="#cb4-6" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-7"><a href="#cb4-7" aria-hidden="true" tabindex="-1"></a><span class="co"># Para otros LMS compatibles con QTI</span></span>
<span id="cb4-8"><a href="#cb4-8" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2qti12</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb4-9"><a href="#cb4-9" aria-hidden="true" tabindex="-1"></a>            <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_qti&#39;</span>,</span>
<span id="cb4-10"><a href="#cb4-10" aria-hidden="true" tabindex="-1"></a>            <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>)</span></code></pre></div>
<h3 id="generar-pdf-para-impresión">📄 Generar PDF (Para impresión)</h3>
<div class="sourceCode" id="cb5"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Requiere LaTeX instalado (TeX Live recomendado)</span></span>
<span id="cb5-2"><a href="#cb5-2" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2pdf</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb5-3"><a href="#cb5-3" aria-hidden="true" tabindex="-1"></a>          <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_pdf&#39;</span>,</span>
<span id="cb5-4"><a href="#cb5-4" aria-hidden="true" tabindex="-1"></a>          <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>,</span>
<span id="cb5-5"><a href="#cb5-5" aria-hidden="true" tabindex="-1"></a>          <span class="at">template =</span> <span class="st">&#39;exam.tex&#39;</span>)</span></code></pre></div>
<h3 id="generar-nops-para-escaneo-automático">📊 Generar NOPS (Para
escaneo automático)</h3>
<div class="sourceCode" id="cb6"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Para corrección automática con escáner</span></span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2nops</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a>           <span class="at">name =</span> <span class="st">&#39;ejercicio_ahorro_nops&#39;</span>,</span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>           <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>,</span>
<span id="cb6-5"><a href="#cb6-5" aria-hidden="true" tabindex="-1"></a>           <span class="at">date =</span> <span class="fu">Sys.Date</span>(),</span>
<span id="cb6-6"><a href="#cb6-6" aria-hidden="true" tabindex="-1"></a>           <span class="at">points =</span> <span class="fu">c</span>(<span class="dv">1</span>, <span class="dv">1</span>, <span class="dv">1</span>, <span class="dv">1</span>))  <span class="co"># Puntos por opción</span></span></code></pre></div>
<h3 id="generar-múltiples-versiones-producción">🎯 Generar Múltiples
Versiones (Producción)</h3>
<div class="sourceCode" id="cb7"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Para examen con 30 versiones diferentes</span></span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="fu">set.seed</span>(<span class="dv">2025</span>)  <span class="co"># Semilla base para reproducibilidad del lote</span></span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a>semillas <span class="ot">&lt;-</span> <span class="fu">sample</span>(<span class="dv">1</span><span class="sc">:</span><span class="dv">50000</span>, <span class="dv">30</span>)  <span class="co"># 30 semillas únicas del rango amplio</span></span>
<span id="cb7-4"><a href="#cb7-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-5"><a href="#cb7-5" aria-hidden="true" tabindex="-1"></a><span class="co"># Crear directorio para las versiones</span></span>
<span id="cb7-6"><a href="#cb7-6" aria-hidden="true" tabindex="-1"></a><span class="fu">dir.create</span>(<span class="st">&#39;./salida/versiones_examen&#39;</span>, <span class="at">showWarnings =</span> <span class="cn">FALSE</span>, <span class="at">recursive =</span> <span class="cn">TRUE</span>)</span>
<span id="cb7-7"><a href="#cb7-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-8"><a href="#cb7-8" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span>(i <span class="cf">in</span> <span class="dv">1</span><span class="sc">:</span><span class="dv">30</span>) {</span>
<span id="cb7-9"><a href="#cb7-9" aria-hidden="true" tabindex="-1"></a>  <span class="fu">set.seed</span>(semillas[i])</span>
<span id="cb7-10"><a href="#cb7-10" aria-hidden="true" tabindex="-1"></a>  <span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb7-11"><a href="#cb7-11" aria-hidden="true" tabindex="-1"></a>             <span class="at">name =</span> <span class="fu">paste0</span>(<span class="st">&#39;ahorro_version_&#39;</span>, <span class="fu">sprintf</span>(<span class="st">&quot;%02d&quot;</span>, i)),</span>
<span id="cb7-12"><a href="#cb7-12" aria-hidden="true" tabindex="-1"></a>             <span class="at">dir =</span> <span class="st">&#39;./salida/versiones_examen/&#39;</span>)</span>
<span id="cb7-13"><a href="#cb7-13" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">&quot;✅ Versión&quot;</span>, i, <span class="st">&quot;generada con semilla&quot;</span>, semillas[i], <span class="st">&quot;</span><span class="sc">\n</span><span class="st">&quot;</span>)</span>
<span id="cb7-14"><a href="#cb7-14" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb7-15"><a href="#cb7-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-16"><a href="#cb7-16" aria-hidden="true" tabindex="-1"></a><span class="co"># Guardar registro de semillas para reproducibilidad total</span></span>
<span id="cb7-17"><a href="#cb7-17" aria-hidden="true" tabindex="-1"></a><span class="fu">write.csv</span>(<span class="fu">data.frame</span>(</span>
<span id="cb7-18"><a href="#cb7-18" aria-hidden="true" tabindex="-1"></a>  <span class="at">version =</span> <span class="dv">1</span><span class="sc">:</span><span class="dv">30</span>,</span>
<span id="cb7-19"><a href="#cb7-19" aria-hidden="true" tabindex="-1"></a>  <span class="at">semilla =</span> semillas,</span>
<span id="cb7-20"><a href="#cb7-20" aria-hidden="true" tabindex="-1"></a>  <span class="at">archivo =</span> <span class="fu">paste0</span>(<span class="st">&#39;ahorro_version_&#39;</span>, <span class="fu">sprintf</span>(<span class="st">&quot;%02d&quot;</span>, <span class="dv">1</span><span class="sc">:</span><span class="dv">30</span>), <span class="st">&#39;.html&#39;</span>),</span>
<span id="cb7-21"><a href="#cb7-21" aria-hidden="true" tabindex="-1"></a>  <span class="at">fecha_generacion =</span> <span class="fu">Sys.time</span>()</span>
<span id="cb7-22"><a href="#cb7-22" aria-hidden="true" tabindex="-1"></a>), <span class="st">&#39;./salida/registro_semillas_ahorro.csv&#39;</span>, <span class="at">row.names =</span> <span class="cn">FALSE</span>)</span>
<span id="cb7-23"><a href="#cb7-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb7-24"><a href="#cb7-24" aria-hidden="true" tabindex="-1"></a><span class="fu">cat</span>(<span class="st">&quot;📋 Registro de semillas guardado en: ./salida/registro_semillas_ahorro.csv</span><span class="sc">\n</span><span class="st">&quot;</span>)</span></code></pre></div>
<h3 id="configuración-avanzada">Configuración Avanzada</h3>
<div class="sourceCode" id="cb8"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Para control total sobre la generación</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a>           <span class="at">name =</span> <span class="st">&#39;ejercicio_configurado&#39;</span>,</span>
<span id="cb8-4"><a href="#cb8-4" aria-hidden="true" tabindex="-1"></a>           <span class="at">dir =</span> <span class="st">&#39;./output/&#39;</span>,</span>
<span id="cb8-5"><a href="#cb8-5" aria-hidden="true" tabindex="-1"></a>           <span class="at">template =</span> <span class="st">&#39;plain.html&#39;</span>,</span>
<span id="cb8-6"><a href="#cb8-6" aria-hidden="true" tabindex="-1"></a>           <span class="at">options =</span> <span class="fu">list</span>(</span>
<span id="cb8-7"><a href="#cb8-7" aria-hidden="true" tabindex="-1"></a>             <span class="at">encoding =</span> <span class="st">&#39;UTF-8&#39;</span>,</span>
<span id="cb8-8"><a href="#cb8-8" aria-hidden="true" tabindex="-1"></a>             <span class="at">converter =</span> <span class="st">&#39;pandoc-mathjax&#39;</span>,</span>
<span id="cb8-9"><a href="#cb8-9" aria-hidden="true" tabindex="-1"></a>             <span class="at">base64 =</span> <span class="cn">FALSE</span></span>
<span id="cb8-10"><a href="#cb8-10" aria-hidden="true" tabindex="-1"></a>           ))</span></code></pre></div>
<h2 id="mejores-prácticas-y-optimización">⚡ Mejores Prácticas y
Optimización</h2>
<h3 id="generación-eficiente">Generación Eficiente</h3>
<div class="sourceCode" id="cb9"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Para grandes cantidades de versiones (100+)</span></span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a><span class="co"># Usar procesamiento en paralelo</span></span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(parallel)</span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>generar_version <span class="ot">&lt;-</span> <span class="cf">function</span>(i) {</span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>  <span class="fu">set.seed</span>(<span class="dv">5000</span> <span class="sc">+</span> i)</span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a>  <span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb9-8"><a href="#cb9-8" aria-hidden="true" tabindex="-1"></a>             <span class="at">name =</span> <span class="fu">paste0</span>(<span class="st">&#39;version_&#39;</span>, i),</span>
<span id="cb9-9"><a href="#cb9-9" aria-hidden="true" tabindex="-1"></a>             <span class="at">dir =</span> <span class="fu">paste0</span>(<span class="st">&#39;./lote_&#39;</span>, <span class="fu">ceiling</span>(i<span class="sc">/</span><span class="dv">50</span>), <span class="st">&#39;/&#39;</span>))</span>
<span id="cb9-10"><a href="#cb9-10" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(i)</span>
<span id="cb9-11"><a href="#cb9-11" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb9-12"><a href="#cb9-12" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb9-13"><a href="#cb9-13" aria-hidden="true" tabindex="-1"></a><span class="co"># Generar 200 versiones en paralelo</span></span>
<span id="cb9-14"><a href="#cb9-14" aria-hidden="true" tabindex="-1"></a>cl <span class="ot">&lt;-</span> <span class="fu">makeCluster</span>(<span class="fu">detectCores</span>() <span class="sc">-</span> <span class="dv">1</span>)</span>
<span id="cb9-15"><a href="#cb9-15" aria-hidden="true" tabindex="-1"></a><span class="fu">clusterEvalQ</span>(cl, <span class="fu">library</span>(exams))</span>
<span id="cb9-16"><a href="#cb9-16" aria-hidden="true" tabindex="-1"></a>versiones <span class="ot">&lt;-</span> <span class="fu">parLapply</span>(cl, <span class="dv">1</span><span class="sc">:</span><span class="dv">200</span>, generar_version)</span>
<span id="cb9-17"><a href="#cb9-17" aria-hidden="true" tabindex="-1"></a><span class="fu">stopCluster</span>(cl)</span></code></pre></div>
<h3 id="control-de-calidad-automatizado">Control de Calidad
Automatizado</h3>
<div class="sourceCode" id="cb10"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Función para verificar balance de opciones</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>verificar_balance <span class="ot">&lt;-</span> <span class="cf">function</span>(<span class="at">n_versiones =</span> <span class="dv">50</span>) {</span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>  resultados <span class="ot">&lt;-</span> <span class="fu">data.frame</span>(</span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a>    <span class="at">version =</span> <span class="dv">1</span><span class="sc">:</span>n_versiones,</span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    <span class="at">opcion_ganadora =</span> <span class="fu">character</span>(n_versiones),</span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="at">eleccion_correcta =</span> <span class="fu">logical</span>(n_versiones),</span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    <span class="at">stringsAsFactors =</span> <span class="cn">FALSE</span></span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>  )</span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a>  <span class="cf">for</span>(i <span class="cf">in</span> <span class="dv">1</span><span class="sc">:</span>n_versiones) {</span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a>    <span class="fu">set.seed</span>(<span class="dv">6000</span> <span class="sc">+</span> i)</span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Simular lógica del archivo para extraer resultados</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>    <span class="co"># ... código de simulación ...</span></span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">&quot;Balance de opciones ganadoras:</span><span class="sc">\n</span><span class="st">&quot;</span>)</span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>  <span class="fu">print</span>(<span class="fu">table</span>(resultados<span class="sc">$</span>opcion_ganadora))</span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">&quot;</span><span class="sc">\n</span><span class="st">Balance de elecciones correctas:</span><span class="sc">\n</span><span class="st">&quot;</span>)</span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>  <span class="fu">print</span>(<span class="fu">table</span>(resultados<span class="sc">$</span>eleccion_correcta))</span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(resultados)</span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a><span class="co"># Ejecutar verificación</span></span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>balance <span class="ot">&lt;-</span> <span class="fu">verificar_balance</span>(<span class="dv">100</span>)</span></code></pre></div>
<h3 id="validación-de-unicidad">Validación de Unicidad</h3>
<div class="sourceCode" id="cb11"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar que las versiones son realmente diferentes</span></span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a>verificar_unicidad <span class="ot">&lt;-</span> <span class="cf">function</span>(semillas) {</span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a>  hashes <span class="ot">&lt;-</span> <span class="fu">character</span>(<span class="fu">length</span>(semillas))</span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a>  <span class="cf">for</span>(i <span class="cf">in</span> <span class="fu">seq_along</span>(semillas)) {</span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a>    <span class="fu">set.seed</span>(semillas[i])</span>
<span id="cb11-7"><a href="#cb11-7" aria-hidden="true" tabindex="-1"></a>    <span class="co"># Generar características únicas de la versión</span></span>
<span id="cb11-8"><a href="#cb11-8" aria-hidden="true" tabindex="-1"></a>    caracteristicas <span class="ot">&lt;-</span> <span class="fu">paste</span>(</span>
<span id="cb11-9"><a href="#cb11-9" aria-hidden="true" tabindex="-1"></a>      <span class="fu">sample</span>(<span class="fu">c</span>(<span class="st">&quot;Ana&quot;</span>, <span class="st">&quot;Carlos&quot;</span>, <span class="st">&quot;Maria&quot;</span>), <span class="dv">1</span>),</span>
<span id="cb11-10"><a href="#cb11-10" aria-hidden="true" tabindex="-1"></a>      <span class="fu">sample</span>(<span class="fu">c</span>(<span class="st">&quot;tio&quot;</span>, <span class="st">&quot;tia&quot;</span>, <span class="st">&quot;abuelo&quot;</span>), <span class="dv">1</span>),</span>
<span id="cb11-11"><a href="#cb11-11" aria-hidden="true" tabindex="-1"></a>      <span class="fu">sample</span>(<span class="fu">seq</span>(<span class="dv">100000</span>, <span class="dv">250000</span>, <span class="dv">25000</span>), <span class="dv">1</span>),</span>
<span id="cb11-12"><a href="#cb11-12" aria-hidden="true" tabindex="-1"></a>      <span class="at">collapse =</span> <span class="st">&quot;_&quot;</span></span>
<span id="cb11-13"><a href="#cb11-13" aria-hidden="true" tabindex="-1"></a>    )</span>
<span id="cb11-14"><a href="#cb11-14" aria-hidden="true" tabindex="-1"></a>    hashes[i] <span class="ot">&lt;-</span> digest<span class="sc">::</span><span class="fu">digest</span>(caracteristicas)</span>
<span id="cb11-15"><a href="#cb11-15" aria-hidden="true" tabindex="-1"></a>  }</span>
<span id="cb11-16"><a href="#cb11-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-17"><a href="#cb11-17" aria-hidden="true" tabindex="-1"></a>  duplicados <span class="ot">&lt;-</span> <span class="fu">sum</span>(<span class="fu">duplicated</span>(hashes))</span>
<span id="cb11-18"><a href="#cb11-18" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">&quot;Versiones únicas:&quot;</span>, <span class="fu">length</span>(<span class="fu">unique</span>(hashes)), <span class="st">&quot;de&quot;</span>, <span class="fu">length</span>(semillas), <span class="st">&quot;</span><span class="sc">\n</span><span class="st">&quot;</span>)</span>
<span id="cb11-19"><a href="#cb11-19" aria-hidden="true" tabindex="-1"></a>  <span class="fu">cat</span>(<span class="st">&quot;Duplicados encontrados:&quot;</span>, duplicados, <span class="st">&quot;</span><span class="sc">\n</span><span class="st">&quot;</span>)</span>
<span id="cb11-20"><a href="#cb11-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-21"><a href="#cb11-21" aria-hidden="true" tabindex="-1"></a>  <span class="fu">return</span>(<span class="fu">length</span>(<span class="fu">unique</span>(hashes)) <span class="sc">==</span> <span class="fu">length</span>(semillas))</span>
<span id="cb11-22"><a href="#cb11-22" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb11-23"><a href="#cb11-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb11-24"><a href="#cb11-24" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar 300 versiones</span></span>
<span id="cb11-25"><a href="#cb11-25" aria-hidden="true" tabindex="-1"></a>semillas_test <span class="ot">&lt;-</span> <span class="fu">sample</span>(<span class="dv">1</span><span class="sc">:</span><span class="dv">50000</span>, <span class="dv">300</span>)</span>
<span id="cb11-26"><a href="#cb11-26" aria-hidden="true" tabindex="-1"></a>unicidad_ok <span class="ot">&lt;-</span> <span class="fu">verificar_unicidad</span>(semillas_test)</span></code></pre></div>
<h2 id="verificaciones-de-calidad">✅ Verificaciones de Calidad</h2>
<h3 id="automáticas">Automáticas</h3>
<ul>
<li>✅ Sin notación científica en ningún formato</li>
<li>✅ Espaciado mejorado en HTML y XML</li>
<li>✅ Caracteres especiales corregidos</li>
<li>✅ Balance matemático entre opciones</li>
<li>✅ Respuestas aleatorias en todas las posiciones</li>
<li>✅ Familiares y nombres variados</li>
<li>✅ Opciones argumentadas y educativas</li>
<li>✅ Explicaciones detalladas y específicas</li>
</ul>
<h3 id="manuales-recomendadas">Manuales Recomendadas</h3>
<ul class="task-list">
<li><label><input type="checkbox" />Verificar balance en lote de 50+
versiones</label></li>
<li><label><input type="checkbox" />Comprobar unicidad en conjunto de
prueba</label></li>
<li><label><input type="checkbox" />Revisar formato en diferentes
navegadores</label></li>
<li><label><input type="checkbox" />Validar importación en LMS
objetivo</label></li>
<li><label><input type="checkbox" />Probar impresión PDF si es
necesario</label></li>
</ul>
<h2 id="contexto-educativo">📚 Contexto Educativo</h2>
<h3 id="competencias-evaluadas">Competencias Evaluadas</h3>
<ul>
<li><strong>Interpretación de datos:</strong> Lectura de tablas con
porcentajes</li>
<li><strong>Cálculo matemático:</strong> Operaciones con porcentajes y
totales</li>
<li><strong>Toma de decisiones:</strong> Evaluación de opciones
financieras</li>
<li><strong>Argumentación:</strong> Justificación de respuestas con
evidencia matemática</li>
</ul>
<h3 id="nivel-de-dificultad">Nivel de Dificultad</h3>
<ul>
<li><strong>Básico:</strong> Lectura de tablas</li>
<li><strong>Intermedio:</strong> Cálculos con porcentajes</li>
<li><strong>Avanzado:</strong> Evaluación y argumentación de
decisiones</li>
</ul>
<h2 id="ejemplos-de-versiones-generadas">💡 Ejemplos de Versiones
Generadas</h2>
<h3 id="ejemplo-1-elección-correcta">Ejemplo 1: Elección Correcta</h3>
<p><strong>Personaje:</strong> Ana elige ayuda de la hermana</p>
<p><strong>Opción 1 (hermana):</strong> 12% constante = $144,000
total</p>
<p><strong>Opción 2 (prima):</strong> 3% + 6% + 16% = $126,000 total</p>
<p><strong>Resultado:</strong> ✅ Elección correcta (hermana da más
dinero)</p>
<h3 id="ejemplo-2-elección-incorrecta">Ejemplo 2: Elección
Incorrecta</h3>
<p><strong>Personaje:</strong> Jorge elige ayuda del tío</p>
<p><strong>Opción 1 (tía):</strong> 13% constante = $175,500 total</p>
<p><strong>Opción 2 (tío):</strong> 2% + 5% + 15% = $148,500 total</p>
<p><strong>Resultado:</strong> ❌ Elección incorrecta (tía da más
dinero)</p>
<h2 id="troubleshooting">🔧 Troubleshooting</h2>
<h3 id="problemas-comunes">Problemas Comunes</h3>
<p><strong>Error: “Browser not installed”</strong></p>
<div class="sourceCode" id="cb12"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Solución: Instalar navegador para Playwright</span></span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a><span class="fu">browser_install_Playwright</span>()</span></code></pre></div>
<p><strong>Error: Notación científica aparece</strong></p>
<div class="sourceCode" id="cb13"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar configuraciones al inicio del archivo</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a><span class="fu">options</span>(<span class="at">scipen =</span> <span class="dv">999</span>)</span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="fu">options</span>(<span class="at">digits =</span> <span class="dv">10</span>)</span></code></pre></div>
<p><strong>Error: Caracteres especiales mal codificados</strong></p>
<div class="sourceCode" id="cb14"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar encoding del archivo</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a><span class="fu">file</span>(<span class="st">&#39;archivo.Rnw&#39;</span>, <span class="at">encoding =</span> <span class="st">&#39;UTF-8&#39;</span>)</span></code></pre></div>
<h3 id="validación-de-resultados">Validación de Resultados</h3>
<p><strong>Verificar balance de opciones:</strong></p>
<div class="sourceCode" id="cb15"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Generar 20 versiones y analizar</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a>resultados <span class="ot">&lt;-</span> <span class="fu">data.frame</span>()</span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span>(i <span class="cf">in</span> <span class="dv">1</span><span class="sc">:</span><span class="dv">20</span>) {</span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">set.seed</span>(<span class="dv">2000</span> <span class="sc">+</span> i)</span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a>  <span class="co"># Extraer totales y determinar ganador</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a>  <span class="co"># Agregar a resultados</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a><span class="fu">table</span>(resultados<span class="sc">$</span>ganador)  <span class="co"># Debe ser ~50/50</span></span></code></pre></div>
<p><strong>Verificar unicidad:</strong></p>
<div class="sourceCode" id="cb16"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Generar versiones con diferentes semillas</span></span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a>versiones <span class="ot">&lt;-</span> <span class="fu">character</span>()</span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a><span class="cf">for</span>(i <span class="cf">in</span> <span class="dv">1</span><span class="sc">:</span><span class="dv">100</span>) {</span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a>  <span class="fu">set.seed</span>(<span class="dv">3000</span> <span class="sc">+</span> i)</span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a>  <span class="co"># Extraer características únicas</span></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a>  <span class="co"># Verificar duplicados</span></span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a>}</span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a><span class="fu">length</span>(<span class="fu">unique</span>(versiones))  <span class="co"># Debe ser 100</span></span></code></pre></div>
<h2 id="lista-de-verificación-pre-uso">📋 Lista de Verificación
Pre-Uso</h2>
<ul class="task-list">
<li><label><input type="checkbox" />R y paquete <code>exams</code>
instalados</label></li>
<li><label><input type="checkbox" />Archivo .Rnw en directorio
correcto</label></li>
<li><label><input type="checkbox" />Permisos de escritura en directorio
de salida</label></li>
<li><label><input type="checkbox" />Navegador instalado (para
HTML)</label></li>
<li><label><input type="checkbox" />LaTeX instalado (para
PDF)</label></li>
<li><label><input type="checkbox" />Encoding UTF-8
configurado</label></li>
</ul>
<h2 id="historial-de-versiones-y-estado-actual">🔄 Historial de
Versiones y Estado Actual</h2>
<h3 id="v2.0-enero-2025---versión-actual---expandida-y-balanceada">✅
v2.0 (Enero 2025) - <strong>VERSIÓN ACTUAL</strong> - Expandida y
Balanceada</h3>
<p><strong>Estado:</strong> 🟢 <strong>COMPLETAMENTE FUNCIONAL Y
OPTIMIZADA</strong></p>
<h4 id="mejoras-implementadas">🚀 Mejoras Implementadas:</h4>
<ul>
<li>✅ <strong>Variables expandidas:</strong> 12 nombres, 8 familiares
con género, 7 montos</li>
<li>✅ <strong>Sistema de balance anti-sesgo:</strong> Aleatorización
inteligente de cuál opción favorece</li>
<li>✅ <strong>Aleatorización completa:</strong> Respuestas en cualquier
posición (A, B, C, D)</li>
<li>✅ <strong>Espaciado mejorado:</strong> Optimizado para HTML, XML y
PDF</li>
<li>✅ <strong>Caracteres especiales:</strong> Completamente
corregidos</li>
<li>✅ <strong>Garantía de unicidad:</strong> 300+ versiones diferentes
verificadas</li>
<li>✅ <strong>Manejo de géneros:</strong> Artículos correctos
automáticos</li>
<li>✅ <strong>Validación de diferencia:</strong> Mínimo $10,000 entre
opciones</li>
<li>✅ <strong>Formatos múltiples:</strong> HTML, PDF, NOPS, Moodle
completamente funcionales</li>
</ul>
<h4 id="archivos-de-salida-generados">📊 Archivos de Salida
Generados:</h4>
<ul>
<li>✅ <code>ahorro_interpretacion_representacion_n2_v2_1.pdf</code> -
Versión PDF</li>
<li>✅ <code>ahorro_interpretacion_representacion_n2_v2_1.docx</code> -
Versión Word</li>
<li>✅
<code>ahorro_interpretacion_representacion_n2_v2_nops_1.pdf</code> -
Versión NOPS</li>
</ul>
<h3 id="v1.0---versión-base-histórica">📜 v1.0 - Versión Base
(Histórica)</h3>
<ul>
<li>✅ Estructura básica del ejercicio</li>
<li>✅ Configuraciones anti-notación científica</li>
<li>✅ Opciones argumentadas implementadas</li>
<li>❌ Sesgo hacia opción 2 (✅ <strong>CORREGIDO</strong> en v2.0)</li>
<li>❌ Respuesta siempre en posición A (✅ <strong>CORREGIDO</strong> en
v2.0)</li>
<li>❌ Variables limitadas (✅ <strong>EXPANDIDO</strong> en v2.0)</li>
</ul>
<h2 id="soporte-y-resolución-de-problemas">📞 Soporte y Resolución de
Problemas</h2>
<h3 id="para-reportar-problemas-o-sugerir-mejoras">🆘 Para Reportar
Problemas o Sugerir Mejoras:</h3>
<ol type="1">
<li><strong>📖 Revisar este README completo</strong> - Muchas dudas
están resueltas aquí</li>
<li><strong>🔧 Verificar configuraciones del sistema</strong> - R,
exams, LaTeX instalados</li>
<li><strong>🎲 Probar con semillas diferentes</strong> -
<code>set.seed(12345)</code> para reproducibilidad</li>
<li><strong>📝 Documentar el error específico</strong> y pasos exactos
para reproducirlo</li>
<li><strong>📂 Verificar estructura de directorios</strong> -
<code>./salida/</code> debe existir</li>
</ol>
<h3 id="comandos-de-diagnóstico-rápido">🔧 Comandos de Diagnóstico
Rápido:</h3>
<div class="sourceCode" id="cb17"><pre class="sourceCode r"><code class="sourceCode r"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar instalación básica</span></span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a><span class="fu">library</span>(exams)</span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a><span class="fu">packageVersion</span>(<span class="st">&quot;exams&quot;</span>)</span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a><span class="co"># Verificar archivo</span></span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a><span class="fu">file.exists</span>(<span class="st">&quot;ahorro_interpretacion_representacion_n2_v2.Rnw&quot;</span>)</span>
<span id="cb17-7"><a href="#cb17-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-8"><a href="#cb17-8" aria-hidden="true" tabindex="-1"></a><span class="co"># Prueba rápida</span></span>
<span id="cb17-9"><a href="#cb17-9" aria-hidden="true" tabindex="-1"></a><span class="fu">set.seed</span>(<span class="dv">12345</span>)</span>
<span id="cb17-10"><a href="#cb17-10" aria-hidden="true" tabindex="-1"></a><span class="fu">exams2html</span>(<span class="st">&#39;ahorro_interpretacion_representacion_n2_v2.Rnw&#39;</span>,</span>
<span id="cb17-11"><a href="#cb17-11" aria-hidden="true" tabindex="-1"></a>           <span class="at">name =</span> <span class="st">&#39;test_diagnostico&#39;</span>, <span class="at">dir =</span> <span class="st">&#39;./salida&#39;</span>)</span></code></pre></div>
<hr />
<h2 id="resumen-ejecutivo">📋 Resumen Ejecutivo</h2>
<p><strong>📁 Archivo:</strong>
<code>ahorro_interpretacion_representacion_n2_v2.Rnw</code> <strong>📅
Última actualización:</strong> Enero 2025 <strong>🔢 Versión:</strong>
2.0 (Expandida y Balanceada) <strong>🎯 Estado:</strong> ✅
<strong>COMPLETAMENTE FUNCIONAL Y OPTIMIZADA</strong> <strong>🔧
Compatibilidad:</strong> R-exams, LaTeX, HTML, XML/Moodle, NOPS
<strong>🎲 Garantía:</strong> 300+ versiones únicas diferentes
verificadas <strong>📊 Formatos soportados:</strong> HTML, PDF, DOCX,
NOPS, Moodle XML <strong>⚡ Tiempo de generación:</strong> &lt; 30
segundos por versión <strong>🎯 Nivel ICFES:</strong> Interpretación y
Representación (Nivel 2)</p>
<h3 id="listo-para-producción">🚀 <strong>LISTO PARA PRODUCCIÓN</strong>
🚀</h3>
</body>
</html>
