<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/ahorro_interpretacion_representacion_n2_v2.Rnw_/Exercise 1</text>
</category>
</question>


<question type="multichoice">
<name>
<text> R1 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Elena quiere ahorrar $100.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (tia):</strong> Al finalizar cada mes, su tia le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$100.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">15%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">15%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">15%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p><strong>Opcion 2 (hermano):</strong> Al finalizar cada mes, su hermano le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$100.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">12%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p>Elena decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la tia. ¿Es correcta la eleccion de Elena?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Elena con cada opcion.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (tia):</strong></p>
<p>Mes 1: $100.000 <span class="math inline">\(\times\)</span> 15% = $15.000</p>
<p>Mes 2: $200.000 <span class="math inline">\(\times\)</span> 15% = $30.000</p>
<p>Mes 3: $300.000 <span class="math inline">\(\times\)</span> 15% = $45.000</p>
<p>Total tia: $90.000</p>
<p>bigskip</p>
<p><strong>Opcion 2 (hermano):</strong></p>
<p>Mes 1: $100.000 <span class="math inline">\(\times\)</span> 4% = $4.000</p>
<p>Mes 2: $200.000 <span class="math inline">\(\times\)</span> 4% = $8.000</p>
<p>Mes 3: $300.000 <span class="math inline">\(\times\)</span> 12% = $36.000</p>
<p>Total hermano: $48.000</p>
<p>bigskip</p>
<p>Por lo tanto, la tia ofrece mas ayuda ($90.000 vs $48.000), por lo que la eleccion de Elena fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la tia solo parece mejor por ser constante pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Elena fue correcta porque la tia ofrece $ 90.000 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la tia es de $ 90.000 mientras que del hermano es de $ 48.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: tia = $ 90.000 y hermano = $ 48.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del hermano es de $ 48.000 mientras que de la tia es de $ 90.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: tia = $ 90.000 y hermano = $ 48.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la tia tiene porcentajes constantes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque la tia efectivamente ofrece mas dinero: $ 90.000 .
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R2 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Maria quiere ahorrar $150.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (primo):</strong> Al finalizar cada mes, su primo le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$150.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">9%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong> Al finalizar cada mes, su tia le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$150.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">2%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">5%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">18%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p>Maria decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la tia. ¿Es correcta la eleccion de Maria?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Maria con cada opcion.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (primo):</strong></p>
<p>Mes 1: $150.000 <span class="math inline">\(\times\)</span> 9% = $13.500</p>
<p>Mes 2: $300.000 <span class="math inline">\(\times\)</span> 9% = $27.000</p>
<p>Mes 3: $450.000 <span class="math inline">\(\times\)</span> 9% = $40.500</p>
<p>Total primo: $81.000</p>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong></p>
<p>Mes 1: $150.000 <span class="math inline">\(\times\)</span> 2% = $3.000</p>
<p>Mes 2: $300.000 <span class="math inline">\(\times\)</span> 5% = $15.000</p>
<p>Mes 3: $450.000 <span class="math inline">\(\times\)</span> 18% = $81.000</p>
<p>Total tia: $99.000</p>
<p>bigskip</p>
<p>Por lo tanto, la tia ofrece mas ayuda ($99.000 vs $81.000), por lo que la eleccion de Maria fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la tia tiene un porcentaje alto en el ultimo mes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque la tia efectivamente ofrece mas dinero: $ 99.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del primo es de $ 81.000 mientras que de la tia es de $ 99.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: tia = $ 99.000 y primo = $ 81.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la tia solo parece mejor por el ultimo mes pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Maria fue correcta porque la tia ofrece $ 99.000 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la tia es de $ 99.000 mientras que del primo es de $ 81.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: tia = $ 99.000 y primo = $ 81.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R3 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Pablo quiere ahorrar $225.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (abuelo):</strong> Al finalizar cada mes, su abuelo le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$225.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$675.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">12%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong> Al finalizar cada mes, su tia le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$225.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">5%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">8%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$675.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">14%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p>Pablo decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del abuelo. ¿Es correcta la eleccion de Pablo?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Pablo con cada opcion.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (abuelo):</strong></p>
<p>Mes 1: $225.000 <span class="math inline">\(\times\)</span> 12% = $27.000</p>
<p>Mes 2: $450.000 <span class="math inline">\(\times\)</span> 12% = $54.000</p>
<p>Mes 3: $675.000 <span class="math inline">\(\times\)</span> 12% = $81.000</p>
<p>Total abuelo: $162.000</p>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong></p>
<p>Mes 1: $225.000 <span class="math inline">\(\times\)</span> 5% = $11.250</p>
<p>Mes 2: $450.000 <span class="math inline">\(\times\)</span> 8% = $36.000</p>
<p>Mes 3: $675.000 <span class="math inline">\(\times\)</span> 14% = $94.500</p>
<p>Total tia: $141.750</p>
<p>bigskip</p>
<p>Por lo tanto, el abuelo ofrece mas ayuda ($162.000 vs $141.750), por lo que la eleccion de Pablo fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total del abuelo es de $ 162.000 mientras que de la tia es de $ 141.750 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: abuelo = $ 162.000 y tia = $ 141.750 . el abuelo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total de la tia es de $ 141.750 mientras que del abuelo es de $ 162.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: abuelo = $ 162.000 y tia = $ 141.750 . el abuelo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque del abuelo solo parece mejor por ser constante pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Pablo fue correcta porque el abuelo ofrece $ 162.000 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque del abuelo tiene porcentajes constantes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque el abuelo efectivamente ofrece mas dinero: $ 162.000 .
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R4 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Maria quiere ahorrar $150.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (primo):</strong> Al finalizar cada mes, su primo le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$150.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">12%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p><strong>Opcion 2 (prima):</strong> Al finalizar cada mes, su prima le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$150.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">6%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">9%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p>Maria decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la prima. ¿Es correcta la eleccion de Maria?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Maria con cada opcion.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (primo):</strong></p>
<p>Mes 1: $150.000 <span class="math inline">\(\times\)</span> 12% = $18.000</p>
<p>Mes 2: $300.000 <span class="math inline">\(\times\)</span> 12% = $36.000</p>
<p>Mes 3: $450.000 <span class="math inline">\(\times\)</span> 12% = $54.000</p>
<p>Total primo: $108.000</p>
<p>bigskip</p>
<p><strong>Opcion 2 (prima):</strong></p>
<p>Mes 1: $150.000 <span class="math inline">\(\times\)</span> 4% = $6.000</p>
<p>Mes 2: $300.000 <span class="math inline">\(\times\)</span> 6% = $18.000</p>
<p>Mes 3: $450.000 <span class="math inline">\(\times\)</span> 9% = $40.500</p>
<p>Total prima: $64.500</p>
<p>bigskip</p>
<p>Por lo tanto, el primo ofrece mas ayuda ($108.000 vs $64.500), por lo que la eleccion de Maria fue incorrecta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la prima no ofrece consistencia en los porcentajes comparado con la otra opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la critica puede tener sentido, la respuesta correcta es ’No’ porque la eleccion fue incorrecta: $ 108.000 vs $ 64.500 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del primo es de $ 108.000 mientras que de la prima es de $ 64.500 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: primo = $ 108.000 y prima = $ 64.500 . el primo ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la prima es de $ 64.500 mientras que del primo es de $ 108.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: primo = $ 108.000 y prima = $ 64.500 . el primo ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la prima parece tener mejor porcentaje final aunque en realidad no sea la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Maria fue incorrecta porque el primo ofrece $ 108.000 mientras que la prima solo ofrece $ 64.500 .
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R5 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Ana quiere ahorrar $250.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (tio):</strong> Al finalizar cada mes, su tio le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$250.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">11%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$500.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">11%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$750.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">11%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong> Al finalizar cada mes, su tia le regala un porcentaje del dinero que tenga acumulado.</p>
<p>medskip</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$250.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$500.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">5%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$750.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">23%</td>
</tr>
</tbody>
</table>
</div>
<p>bigskip</p>
<p>Ana decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la tia. ¿Es correcta la eleccion de Ana?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Ana con cada opcion.</p>
<p>bigskip</p>
<p><strong>Opcion 1 (tio):</strong></p>
<p>Mes 1: $250.000 <span class="math inline">\(\times\)</span> 11% = $27.500</p>
<p>Mes 2: $500.000 <span class="math inline">\(\times\)</span> 11% = $55.000</p>
<p>Mes 3: $750.000 <span class="math inline">\(\times\)</span> 11% = $82.500</p>
<p>Total tio: $165.000</p>
<p>bigskip</p>
<p><strong>Opcion 2 (tia):</strong></p>
<p>Mes 1: $250.000 <span class="math inline">\(\times\)</span> 4% = $10.000</p>
<p>Mes 2: $500.000 <span class="math inline">\(\times\)</span> 5% = $25.000</p>
<p>Mes 3: $750.000 <span class="math inline">\(\times\)</span> 23% = $172.500</p>
<p>Total tia: $207.500</p>
<p>bigskip</p>
<p>Por lo tanto, la tia ofrece mas ayuda ($207.500 vs $165.000), por lo que la eleccion de Ana fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la tia es de $ 207.500 mientras que del tio es de $ 165.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: tia = $ 207.500 y tio = $ 165.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la tia solo parece mejor por el ultimo mes pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Ana fue correcta porque la tia ofrece $ 207.500 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la tia tiene un porcentaje alto en el ultimo mes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque la tia efectivamente ofrece mas dinero: $ 207.500 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del tio es de $ 165.000 mientras que de la tia es de $ 207.500 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: tia = $ 207.500 y tio = $ 165.000 . la tia ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
