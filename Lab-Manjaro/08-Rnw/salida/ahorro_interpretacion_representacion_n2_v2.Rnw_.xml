<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/ahorro_interpretacion_representacion_n2_v2.Rnw_/Exercise 1</text>
</category>
</question>


<question type="multichoice">
<name>
<text> R1 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Carmen quiere ahorrar $200.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (abuelo):</strong> Al finalizar cada mes, su abuelo le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">13%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$400.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">13%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$600.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">13%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (hermano):</strong> Al finalizar cada mes, su hermano le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">3%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$400.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">7%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$600.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">14%</td>
</tr>
</tbody>
</table>
</div>
<p>Carmen decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del abuelo. ¿Es correcta la eleccion de Carmen?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Carmen con cada opcion.</p>
<p><strong>Opcion 1 (abuelo):</strong></p>
<p>Mes 1: $200.000 <span class="math inline">\(\times\)</span> 13% = $26.000</p>
<p>Mes 2: $400.000 <span class="math inline">\(\times\)</span> 13% = $52.000</p>
<p>Mes 3: $600.000 <span class="math inline">\(\times\)</span> 13% = $78.000</p>
<p>Total abuelo: $156.000</p>
<p><strong>Opcion 2 (hermano):</strong></p>
<p>Mes 1: $200.000 <span class="math inline">\(\times\)</span> 3% = $6.000</p>
<p>Mes 2: $400.000 <span class="math inline">\(\times\)</span> 7% = $28.000</p>
<p>Mes 3: $600.000 <span class="math inline">\(\times\)</span> 14% = $84.000</p>
<p>Total hermano: $118.000</p>
<p>Por lo tanto, el abuelo ofrece mas ayuda ($156.000 vs $118.000), por lo que la eleccion de Carmen fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque del abuelo solo parece mejor por ser constante pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Carmen fue correcta porque el abuelo ofrece $ 156.000 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque del abuelo tiene porcentajes constantes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque el abuelo efectivamente ofrece mas dinero: $ 156.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del hermano es de $ 118.000 mientras que del abuelo es de $ 156.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: abuelo = $ 156.000 y hermano = $ 118.000 . el abuelo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total del abuelo es de $ 156.000 mientras que del hermano es de $ 118.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: abuelo = $ 156.000 y hermano = $ 118.000 . el abuelo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R2 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Maria quiere ahorrar $200.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (hermano):</strong> Al finalizar cada mes, su hermano le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$400.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">12%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$600.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">12%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (hermana):</strong> Al finalizar cada mes, su hermana le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">3%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$400.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">6%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$600.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">9%</td>
</tr>
</tbody>
</table>
</div>
<p>Maria decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la hermana. ¿Es correcta la eleccion de Maria?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Maria con cada opcion.</p>
<p><strong>Opcion 1 (hermano):</strong></p>
<p>Mes 1: $200.000 <span class="math inline">\(\times\)</span> 12% = $24.000</p>
<p>Mes 2: $400.000 <span class="math inline">\(\times\)</span> 12% = $48.000</p>
<p>Mes 3: $600.000 <span class="math inline">\(\times\)</span> 12% = $72.000</p>
<p>Total hermano: $144.000</p>
<p><strong>Opcion 2 (hermana):</strong></p>
<p>Mes 1: $200.000 <span class="math inline">\(\times\)</span> 3% = $6.000</p>
<p>Mes 2: $400.000 <span class="math inline">\(\times\)</span> 6% = $24.000</p>
<p>Mes 3: $600.000 <span class="math inline">\(\times\)</span> 9% = $54.000</p>
<p>Total hermana: $84.000</p>
<p>Por lo tanto, el hermano ofrece mas ayuda ($144.000 vs $84.000), por lo que la eleccion de Maria fue incorrecta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la hermana parece tener mejor porcentaje final aunque en realidad no sea la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Maria fue incorrecta porque el hermano ofrece $ 144.000 mientras que la hermana solo ofrece $ 84.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la hermana no ofrece consistencia en los porcentajes comparado con la otra opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la critica puede tener sentido, la respuesta correcta es ’No’ porque la eleccion fue incorrecta: $ 144.000 vs $ 84.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la hermana es de $ 84.000 mientras que del hermano es de $ 144.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: hermano = $ 144.000 y hermana = $ 84.000 . el hermano ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del hermano es de $ 144.000 mientras que de la hermana es de $ 84.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: hermano = $ 144.000 y hermana = $ 84.000 . el hermano ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R3 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Sofia quiere ahorrar $250.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (primo):</strong> Al finalizar cada mes, su primo le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$250.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">14%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$500.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">14%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$750.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">14%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (abuela):</strong> Al finalizar cada mes, su abuela le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$250.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$500.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">6%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$750.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">14%</td>
</tr>
</tbody>
</table>
</div>
<p>Sofia decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del primo. ¿Es correcta la eleccion de Sofia?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Sofia con cada opcion.</p>
<p><strong>Opcion 1 (primo):</strong></p>
<p>Mes 1: $250.000 <span class="math inline">\(\times\)</span> 14% = $35.000</p>
<p>Mes 2: $500.000 <span class="math inline">\(\times\)</span> 14% = $70.000</p>
<p>Mes 3: $750.000 <span class="math inline">\(\times\)</span> 14% = $105.000</p>
<p>Total primo: $210.000</p>
<p><strong>Opcion 2 (abuela):</strong></p>
<p>Mes 1: $250.000 <span class="math inline">\(\times\)</span> 4% = $10.000</p>
<p>Mes 2: $500.000 <span class="math inline">\(\times\)</span> 6% = $30.000</p>
<p>Mes 3: $750.000 <span class="math inline">\(\times\)</span> 14% = $105.000</p>
<p>Total abuela: $145.000</p>
<p>Por lo tanto, el primo ofrece mas ayuda ($210.000 vs $145.000), por lo que la eleccion de Sofia fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque del primo solo parece mejor por ser constante pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Sofia fue correcta porque el primo ofrece $ 210.000 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total de la abuela es de $ 145.000 mientras que del primo es de $ 210.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: primo = $ 210.000 y abuela = $ 145.000 . el primo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque del primo tiene porcentajes constantes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque el primo efectivamente ofrece mas dinero: $ 210.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total del primo es de $ 210.000 mientras que de la abuela es de $ 145.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: primo = $ 210.000 y abuela = $ 145.000 . el primo ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R4 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Jorge quiere ahorrar $175.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (hermano):</strong> Al finalizar cada mes, su hermano le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$175.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">10%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$350.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">10%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$525.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">10%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (prima):</strong> Al finalizar cada mes, su prima le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$175.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">1%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$350.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">6%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$525.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">19%</td>
</tr>
</tbody>
</table>
</div>
<p>Jorge decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda de la prima. ¿Es correcta la eleccion de Jorge?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Jorge con cada opcion.</p>
<p><strong>Opcion 1 (hermano):</strong></p>
<p>Mes 1: $175.000 <span class="math inline">\(\times\)</span> 10% = $17.500</p>
<p>Mes 2: $350.000 <span class="math inline">\(\times\)</span> 10% = $35.000</p>
<p>Mes 3: $525.000 <span class="math inline">\(\times\)</span> 10% = $52.500</p>
<p>Total hermano: $105.000</p>
<p><strong>Opcion 2 (prima):</strong></p>
<p>Mes 1: $175.000 <span class="math inline">\(\times\)</span> 1% = $1.750</p>
<p>Mes 2: $350.000 <span class="math inline">\(\times\)</span> 6% = $21.000</p>
<p>Mes 3: $525.000 <span class="math inline">\(\times\)</span> 19% = $99.750</p>
<p>Total prima: $122.500</p>
<p>Por lo tanto, la prima ofrece mas ayuda ($122.500 vs $105.000), por lo que la eleccion de Jorge fue correcta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque la ayuda total del hermano es de $ 105.000 mientras que de la prima es de $ 122.500 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: prima = $ 122.500 y hermano = $ 105.000 . la prima ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque de la prima solo parece mejor por el ultimo mes pero en realidad si es la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Jorge fue correcta porque la prima ofrece $ 122.500 que es efectivamente la mayor cantidad.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque de la prima tiene un porcentaje alto en el ultimo mes que compensa las diferencias de los otros meses.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la justificacion puede parecer valida, la respuesta correcta es ’Si’ porque la prima efectivamente ofrece mas dinero: $ 122.500 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total de la prima es de $ 122.500 mientras que del hermano es de $ 105.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: prima = $ 122.500 y hermano = $ 105.000 . la prima ofrece mas ayuda, por lo que la eleccion fue correcta.
</p>]]></text>
</feedback>
</answer>
</question>


<question type="multichoice">
<name>
<text> R5 Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Andres quiere ahorrar $100.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (abuelo):</strong> Al finalizar cada mes, su abuelo le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$100.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">9%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (prima):</strong> Al finalizar cada mes, su prima le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$100.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">4%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$200.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">6%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$300.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">18%</td>
</tr>
</tbody>
</table>
</div>
<p>Andres decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del abuelo. ¿Es correcta la eleccion de Andres?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Andres con cada opcion.</p>
<p><strong>Opcion 1 (abuelo):</strong></p>
<p>Mes 1: $100.000 <span class="math inline">\(\times\)</span> 9% = $9.000</p>
<p>Mes 2: $200.000 <span class="math inline">\(\times\)</span> 9% = $18.000</p>
<p>Mes 3: $300.000 <span class="math inline">\(\times\)</span> 9% = $27.000</p>
<p>Total abuelo: $54.000</p>
<p><strong>Opcion 2 (prima):</strong></p>
<p>Mes 1: $100.000 <span class="math inline">\(\times\)</span> 4% = $4.000</p>
<p>Mes 2: $200.000 <span class="math inline">\(\times\)</span> 6% = $12.000</p>
<p>Mes 3: $300.000 <span class="math inline">\(\times\)</span> 18% = $54.000</p>
<p>Total prima: $70.000</p>
<p>Por lo tanto, la prima ofrece mas ayuda ($70.000 vs $54.000), por lo que la eleccion de Andres fue incorrecta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total del abuelo es de $ 54.000 mientras que de la prima es de $ 70.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: prima = $ 70.000 y abuelo = $ 54.000 . la prima ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque del abuelo parece tener porcentajes mas estables aunque en realidad no sea la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Andres fue incorrecta porque la prima ofrece $ 70.000 mientras que el abuelo solo ofrece $ 54.000 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="100" format="html">
<text><![CDATA[<p>
No, porque la ayuda total de la prima es de $ 70.000 mientras que del abuelo es de $ 54.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: prima = $ 70.000 y abuelo = $ 54.000 . la prima ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque del abuelo no ofrece suficiente dinero total comparado con la otra opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la critica puede tener sentido, la respuesta correcta es ’No’ porque la eleccion fue incorrecta: $ 70.000 vs $ 54.000 .
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
