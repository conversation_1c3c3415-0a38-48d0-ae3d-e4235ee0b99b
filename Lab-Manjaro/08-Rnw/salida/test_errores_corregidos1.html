<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

<head>
<title>Exam 1</title>
<style type="text/css">
body{font-family: Arial, Helvetica, Sans;}
</style>
<meta charset="utf-8" />
</head>

<body>
<h2>Exam 1</h2>

<ol>
<li>
<h4>Question</h4>
Carlos quiere ahorrar $225.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
<b>Opcion 1 (tio):</b> Al finalizar cada mes, su tio le regala un porcentaje del dinero que tenga acumulado.
<div class="p"><!----></div>
medskip
<div class="p"><!----></div>
<div style="text-align:center">
<table border="1">
<tr><td align="center"><b>Ahorro Acumulado</b> </td><td align="center"><b>Mes</b> </td><td align="center"><b>Porcentaje regalado</b> </td></tr>
<tr><td align="center">$225.000 </td><td align="center">1 </td><td align="center">9% </td></tr>
<tr><td align="center">$450.000 </td><td align="center">2 </td><td align="center">9% </td></tr>
<tr><td align="center">$675.000 </td><td align="center">3 </td><td align="center">9% </td></tr></table>
</div>
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
<b>Opcion 2 (prima):</b> Al finalizar cada mes, su prima le regala un porcentaje del dinero que tenga acumulado.
<div class="p"><!----></div>
medskip
<div class="p"><!----></div>
<div style="text-align:center">
<table border="1">
<tr><td align="center"><b>Ahorro Acumulado</b> </td><td align="center"><b>Mes</b> </td><td align="center"><b>Porcentaje regalado</b> </td></tr>
<tr><td align="center">$225.000 </td><td align="center">1 </td><td align="center">3% </td></tr>
<tr><td align="center">$450.000 </td><td align="center">2 </td><td align="center">3% </td></tr>
<tr><td align="center">$675.000 </td><td align="center">3 </td><td align="center">21% </td></tr></table>
</div>
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
Carlos decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del tio. ¿Es correcta la eleccion de Carlos?
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
No, porque la ayuda total de la prima es de $ 162.000 mientras que del tio es de $ 121.500 .
</li>
<li>
No, porque del tio no ofrece suficiente dinero total comparado con la otra opcion.
</li>
<li>
Si, porque la ayuda total del tio es de $ 121.500 mientras que de la prima es de $ 162.000 .
</li>
<li>
Si, porque del tio parece tener porcentajes mas estables aunque en realidad no sea la mejor opcion.
</li>
</ol>
<br/>
<h4>Solution</h4>
Para resolver este problema, debemos calcular la ayuda total que recibiria Carlos con cada opcion.
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
<b>Opcion 1 (tio):</b>
<div class="p"><!----></div>
Mes 1: $225.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 9% = $20.250
<div class="p"><!----></div>
Mes 2: $450.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 9% = $40.500
<div class="p"><!----></div>
Mes 3: $675.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 9% = $60.750
<div class="p"><!----></div>
Total tio: $121.500
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
<b>Opcion 2 (prima):</b>
<div class="p"><!----></div>
Mes 1: $225.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 3% = $6.750
<div class="p"><!----></div>
Mes 2: $450.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 3% = $13.500
<div class="p"><!----></div>
Mes 3: $675.000 <math xmlns="http://www.w3.org/1998/Math/MathML">
<mrow><mo>&times;</mo></mrow></math> 21% = $141.750
<div class="p"><!----></div>
Total prima: $162.000
<div class="p"><!----></div>
bigskip
<div class="p"><!----></div>
Por lo tanto, la prima ofrece mas ayuda ($162.000 vs $121.500), por lo que la eleccion de Carlos fue incorrecta.
<div class="p"><!----></div>
<br/>
<ol type="a">
<li>
Correcto. Al calcular los totales:  prima  = $ 162.000  y  tio  = $ 121.500 .  la   prima  ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</li>
<li>
Incorrecto. Aunque la critica puede tener sentido, la respuesta correcta es 'No' porque la eleccion fue incorrecta:  $ 162.000  vs $ 121.500 .
</li>
<li>
Incorrecto. Al calcular los totales:  prima  = $ 162.000  y  tio  = $ 121.500 .  la   prima  ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</li>
<li>
Incorrecto. La eleccion de  Carlos  fue incorrecta porque  la   prima  ofrece $ 162.000  mientras que  el   tio  solo ofrece $ 121.500 .
</li>
</ol>
<br/>
</li>
</ol>

</body>
</html>
