<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/test_sin_latex/Exercise 1</text>
</category>
</question>


<question type="multichoice">
<name>
<text> Q1 : ahorro_interpretacion_representacion_n2_v2 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>Carlos quiere ahorrar $225.000 cada mes durante 3 meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.</p>
<p><strong>Opcion 1 (tio):</strong> Al finalizar cada mes, su tio le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$225.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">9%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$675.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">9%</td>
</tr>
</tbody>
</table>
</div>
<p><strong>Opcion 2 (prima):</strong> Al finalizar cada mes, su prima le regala un porcentaje del dinero que tenga acumulado.</p>
<div class="center">
<table class="table_shade">
<thead>
<tr class="header">
<th style="text-align: center;"><strong>Ahorro Acumulado</strong></th>
<th style="text-align: center;"><strong>Mes</strong></th>
<th style="text-align: center;"><strong>Porcentaje regalado</strong></th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: center;">$225.000</td>
<td style="text-align: center;">1</td>
<td style="text-align: center;">3%</td>
</tr>
<tr class="even">
<td style="text-align: center;">$450.000</td>
<td style="text-align: center;">2</td>
<td style="text-align: center;">3%</td>
</tr>
<tr class="odd">
<td style="text-align: center;">$675.000</td>
<td style="text-align: center;">3</td>
<td style="text-align: center;">21%</td>
</tr>
</tbody>
</table>
</div>
<p>Carlos decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda del tio. ¿Es correcta la eleccion de Carlos?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para resolver este problema, debemos calcular la ayuda total que recibiria Carlos con cada opcion.</p>
<p><strong>Opcion 1 (tio):</strong></p>
<p>Mes 1: $225.000 <span class="math inline">\(\times\)</span> 9% = $20.250</p>
<p>Mes 2: $450.000 <span class="math inline">\(\times\)</span> 9% = $40.500</p>
<p>Mes 3: $675.000 <span class="math inline">\(\times\)</span> 9% = $60.750</p>
<p>Total tio: $121.500</p>
<p><strong>Opcion 2 (prima):</strong></p>
<p>Mes 1: $225.000 <span class="math inline">\(\times\)</span> 3% = $6.750</p>
<p>Mes 2: $450.000 <span class="math inline">\(\times\)</span> 3% = $13.500</p>
<p>Mes 3: $675.000 <span class="math inline">\(\times\)</span> 21% = $141.750</p>
<p>Total prima: $162.000</p>
<p>Por lo tanto, la prima ofrece mas ayuda ($162.000 vs $121.500), por lo que la eleccion de Carlos fue incorrecta.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>false</shuffleanswers>
<single>true</single>
<answernumbering>abc</answernumbering>
<answer fraction="100" format="html">
<text><![CDATA[<p>
No, porque la ayuda total de la prima es de $ 162.000 mientras que del tio es de $ 121.500 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Correcto. Al calcular los totales: prima = $ 162.000 y tio = $ 121.500 . la prima ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
No, porque del tio no ofrece suficiente dinero total comparado con la otra opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Aunque la critica puede tener sentido, la respuesta correcta es ’No’ porque la eleccion fue incorrecta: $ 162.000 vs $ 121.500 .
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque la ayuda total del tio es de $ 121.500 mientras que de la prima es de $ 162.000 .
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. Al calcular los totales: prima = $ 162.000 y tio = $ 121.500 . la prima ofrece mas ayuda, por lo que la eleccion fue incorrecta.
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Si, porque del tio parece tener porcentajes mas estables aunque en realidad no sea la mejor opcion.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Incorrecto. La eleccion de Carlos fue incorrecta porque la prima ofrece $ 162.000 mientras que el tio solo ofrece $ 121.500 .
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
